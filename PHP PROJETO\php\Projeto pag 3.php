<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar permissão para acessar a página de orçamentos
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')) {
    header("Location: Projeto.php?msg=Você não tem permissão para acessar esta página");
    exit;
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se a tabela orcamentos existe
$table_exists = false;
$result_check = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
if (mysqli_num_rows($result_check) > 0) {
    $table_exists = true;
}

// Se a tabela não existir, criar
if (!$table_exists) {
    $create_table_query = "
    CREATE TABLE IF NOT EXISTS `orcamentos` (
      `id` INT NOT NULL AUTO_INCREMENT,
      `obra_id` INT NOT NULL,
      `descricao` TEXT NOT NULL,
      `valor` DECIMAL(15, 2) NOT NULL,
      `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      `data_aprovacao` DATE NULL,
      `status` VARCHAR(50) DEFAULT 'Em análise',
      PRIMARY KEY (`id`),
      CONSTRAINT `fk_orcamentos_obras`
        FOREIGN KEY (`obra_id`)
        REFERENCES `obras` (`obras_id`)
        ON DELETE CASCADE
        ON UPDATE CASCADE
    )";

    mysqli_query($conn, $create_table_query);

    // Verificar novamente se a tabela foi criada
    $result_check = mysqli_query($conn, "SHOW TABLES LIKE 'orcamentos'");
    if (mysqli_num_rows($result_check) > 0) {
        $table_exists = true;
        $_SESSION['mensagem'] = "Tabela de orçamentos criada com sucesso!";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao criar tabela de orçamentos. Entre em contato com o administrador.";
        $_SESSION['tipo_mensagem'] = "danger";
    }
}

// Buscar todos os orçamentos se a tabela existir
$num_orcamentos = 0;
$result_orcamentos = null;

if ($table_exists) {
    $query_orcamentos = "SELECT o.*, ob.nome_obra
                         FROM orcamentos o
                         JOIN obras ob ON o.obra_id = ob.obras_id
                         ORDER BY o.data_criacao DESC";
    $result_orcamentos = mysqli_query($conn, $query_orcamentos);
    $num_orcamentos = mysqli_num_rows($result_orcamentos);
}

// Função para determinar a cor do status
function getStatusColor($status) {
    switch ($status) {
        case 'Aprovado':
            return 'success';
        case 'Em análise':
            return 'warning';
        case 'Rejeitado':
            return 'danger';
        case 'Concluído':
            return 'info';
        default:
            return 'secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Orçamentos - Built Organizer</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(!isset($_SESSION['cargo_utilizador']) || checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php" class="active">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Mensagens de alerta -->
            <?php if(isset($_SESSION['mensagem'])): ?>
                <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['mensagem']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
                <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
            <?php endif; ?>

            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-cash-coin"></i> Gestão de Orçamentos</h1>
                    <p>Gerencie todos os orçamentos das obras da sua empresa em um só lugar.</p>
                </div>
            </div>

            <div class="container">
                <!-- Botões de ação -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#adicionarOrcamentoModal">
                        <i class="bi bi-plus-circle"></i> Adicionar Novo Orçamento
                    </button>
                    <!-- Botão de relatório removido -->
                </div>

                <!-- Tabela de Orçamentos -->
                <div class="obras-table">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Obra</th>
                                    <th scope="col">Descrição</th>
                                    <th scope="col">Valor</th>
                                    <th scope="col">Data de Criação</th>
                                    <th scope="col">Data de Aprovação</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($table_exists && $num_orcamentos > 0): ?>
                                    <?php $contador = 1; ?>
                                    <?php while($orcamento = mysqli_fetch_assoc($result_orcamentos)): ?>
                                        <tr>
                                            <td><?php echo $contador++; ?></td>
                                            <td><?php echo htmlspecialchars($orcamento['nome_obra']); ?></td>
                                            <td><?php echo htmlspecialchars($orcamento['descricao']); ?></td>
                                            <td>€<?php echo number_format((float)$orcamento['valor'], 0, ',', '.'); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($orcamento['data_criacao'])); ?></td>
                                            <td>
                                                <?php if(isset($orcamento['data_aprovacao']) && $orcamento['data_aprovacao']): ?>
                                                    <?php echo date('d/m/Y', strtotime($orcamento['data_aprovacao'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Pendente</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="status-badge bg-<?php echo getStatusColor($orcamento['status']); ?>">
                                                    <?php echo htmlspecialchars($orcamento['status']); ?>
                                                </span>
                                            </td>
                                            <td class="action-cell">
                                                <a href="detalhes_orcamento.php?id=<?php echo $orcamento['id']; ?>" class="btn btn-info btn-sm text-white" data-bs-toggle="tooltip" title="Ver Detalhes">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-warning btn-sm" data-bs-toggle="tooltip" title="Editar Orçamento" onclick="editarOrcamento(
                                                    '<?php echo $orcamento['id']; ?>',
                                                    '<?php echo $orcamento['obra_id']; ?>',
                                                    '<?php echo htmlspecialchars($orcamento['descricao']); ?>',
                                                    '<?php echo $orcamento['valor']; ?>',
                                                    '<?php echo htmlspecialchars($orcamento['status']); ?>'
                                                )" data-bs-toggle="modal" data-bs-target="#editarOrcamentoModal">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <?php if($orcamento['status'] == 'Em análise'): ?>
                                                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="tooltip" title="Aprovar Orçamento" onclick="aprovarOrcamento('<?php echo $orcamento['id']; ?>')">
                                                    <i class="bi bi-check-circle"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="tooltip" title="Rejeitar Orçamento" onclick="rejeitarOrcamento('<?php echo $orcamento['id']; ?>')">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-3">
                                            <i class="bi bi-exclamation-triangle me-2"></i> Nenhum orçamento cadastrado. Clique em "Adicionar Novo Orçamento" para começar.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Modal para Adicionar Novo Orçamento -->
    <div class="modal fade" id="adicionarOrcamentoModal" tabindex="-1" aria-labelledby="adicionarOrcamentoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adicionarOrcamentoModalLabel">Adicionar Novo Orçamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formAdicionarOrcamento" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="acao" value="adicionar">

                        <div class="mb-3">
                            <label for="obra_id" class="form-label">Obra *</label>
                            <select class="form-select" id="obra_id" name="obra_id" required>
                                <option value="">Selecione uma obra</option>
                                <?php
                                // Buscar todas as obras
                                $query_obras = "SELECT * FROM obras ORDER BY nome_obra";
                                $result_obras = mysqli_query($conn, $query_obras);

                                if ($result_obras && mysqli_num_rows($result_obras) > 0) {
                                    while ($obra = mysqli_fetch_assoc($result_obras)) {
                                        echo "<option value='" . $obra['obras_id'] . "'>" . htmlspecialchars($obra['nome_obra']) . "</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição *</label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="valor" class="form-label">Valor (€) *</label>
                            <input type="number" step="0.01" class="form-control" id="valor" name="valor" required>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Em análise" selected>Em análise</option>
                                <option value="Aprovado">Aprovado</option>
                                <option value="Rejeitado">Rejeitado</option>
                                <option value="Concluído">Concluído</option>
                            </select>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Adicionar Orçamento</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Orçamento -->
    <div class="modal fade" id="editarOrcamentoModal" tabindex="-1" aria-labelledby="editarOrcamentoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarOrcamentoModalLabel">Editar Orçamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarOrcamento" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" id="id_orcamento_edit" name="id_orcamento">

                        <div class="mb-3">
                            <label for="obra_id_edit" class="form-label">Obra *</label>
                            <select class="form-select" id="obra_id_edit" name="obra_id" required>
                                <option value="">Selecione uma obra</option>
                                <?php
                                // Buscar todas as obras
                                $query_obras = "SELECT * FROM obras ORDER BY nome_obra";
                                $result_obras = mysqli_query($conn, $query_obras);

                                if ($result_obras && mysqli_num_rows($result_obras) > 0) {
                                    while ($obra = mysqli_fetch_assoc($result_obras)) {
                                        echo "<option value='" . $obra['obras_id'] . "'>" . htmlspecialchars($obra['nome_obra']) . "</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="descricao_edit" class="form-label">Descrição *</label>
                            <textarea class="form-control" id="descricao_edit" name="descricao" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="valor_edit" class="form-label">Valor (€) *</label>
                            <input type="number" step="0.01" class="form-control" id="valor_edit" name="valor" required>
                        </div>

                        <div class="mb-3">
                            <label for="status_edit" class="form-label">Status *</label>
                            <select class="form-select" id="status_edit" name="status" required>
                                <option value="Em análise">Em análise</option>
                                <option value="Aprovado">Aprovado</option>
                                <option value="Rejeitado">Rejeitado</option>
                                <option value="Concluído">Concluído</option>
                            </select>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>

    <!-- Scripts para corrigir problemas com tokens CSRF -->
    <script src="csrf_fix.js"></script>
    <script src="csrf_init.js"></script>





    <!-- JavaScript personalizado -->
    <script>
        // Inicializar tooltips do Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Função para editar orçamento
        function editarOrcamento(id, obra_id, descricao, valor, status) {
            document.getElementById('id_orcamento_edit').value = id;
            document.getElementById('obra_id_edit').value = obra_id;
            document.getElementById('descricao_edit').value = descricao;
            document.getElementById('valor_edit').value = valor;
            document.getElementById('status_edit').value = status;

            // Abrir modal
            var editarOrcamentoModal = new bootstrap.Modal(document.getElementById('editarOrcamentoModal'));
            editarOrcamentoModal.show();
        }

        // Função para aprovar orçamento
        function aprovarOrcamento(id) {
            if (confirm('Tem certeza que deseja aprovar este orçamento?')) {
                // Obter token CSRF
                fetch('get_csrf_token.php')
                .then(response => response.json())
                .then(tokenData => {
                    if (tokenData.token) {
                        // Construir URL com token CSRF
                        const url = `processar_orcamento.php?acao=aprovar&id=${id}&csrf_token=${tokenData.token}`;

                        // Fazer a requisição com o token
                        fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                location.reload();
                            } else {
                                alert('Erro: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Erro:', error);
                            alert('Ocorreu um erro ao processar a solicitação.');
                        });
                    } else {
                        alert('Erro de segurança: Não foi possível obter o token CSRF.');
                    }
                })
                .catch(error => {
                    console.error('Erro ao obter token CSRF:', error);
                    alert('Erro de segurança: Não foi possível obter o token CSRF.');
                });
            }
        }

        // Função para rejeitar orçamento
        function rejeitarOrcamento(id) {
            if (confirm('Tem certeza que deseja rejeitar este orçamento?')) {
                // Obter token CSRF
                fetch('get_csrf_token.php')
                .then(response => response.json())
                .then(tokenData => {
                    if (tokenData.token) {
                        // Construir URL com token CSRF
                        const url = `processar_orcamento.php?acao=rejeitar&id=${id}&csrf_token=${tokenData.token}`;

                        // Fazer a requisição com o token
                        fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                location.reload();
                            } else {
                                alert('Erro: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Erro:', error);
                            alert('Ocorreu um erro ao processar a solicitação.');
                        });
                    } else {
                        alert('Erro de segurança: Não foi possível obter o token CSRF.');
                    }
                })
                .catch(error => {
                    console.error('Erro ao obter token CSRF:', error);
                    alert('Erro de segurança: Não foi possível obter o token CSRF.');
                });
            }
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Processar o formulário de adicionar orçamento via AJAX
            const formAdicionarOrcamento = document.getElementById('formAdicionarOrcamento');

            formAdicionarOrcamento.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(formAdicionarOrcamento);

                fetch('processar_orcamento.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('adicionarOrcamentoModal'));
                        modal.hide();

                        // Exibir mensagem de sucesso
                        alert(data.message);

                        // Recarregar a página para mostrar o novo orçamento
                        location.reload();
                    } else {
                        // Exibir mensagem de erro
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro ao processar a solicitação.');
                });
            });

            // Processar o formulário de editar orçamento via AJAX
            const formEditarOrcamento = document.getElementById('formEditarOrcamento');

            formEditarOrcamento.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(formEditarOrcamento);

                fetch('processar_orcamento.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editarOrcamentoModal'));
                        modal.hide();

                        // Exibir mensagem de sucesso
                        alert(data.message);

                        // Recarregar a página para mostrar as alterações
                        location.reload();
                    } else {
                        // Exibir mensagem de erro
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro ao processar a solicitação.');
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
            
            // Verificar se o botão de adicionar orçamento está funcionando
            const btnAdicionar = document.querySelector('button[data-bs-target="#adicionarOrcamentoModal"]');
            if (btnAdicionar) {
                btnAdicionar.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('adicionarOrcamentoModal'));
                    modal.show();
                });
            }
        });
    </script>
</body>
</html>
